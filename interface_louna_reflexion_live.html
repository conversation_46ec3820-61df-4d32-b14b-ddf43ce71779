<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Réflexion en Temps Réel</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-weight: bold;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-idle { background: #4CAF50; }
        .status-thinking { background: #FF9800; }
        .status-memory { background: #2196F3; }
        .status-internet { background: #9C27B0; }
        .status-error { background: #F44336; }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .main-container {
            flex: 1;
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            gap: 20px;
            padding: 20px;
        }

        .chat-container {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .reflection-container {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        /* STYLES POUR LA RÉFLEXION EN LIVE */
        .reflection-panel {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            border: 2px solid #667eea;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
            position: relative;
            overflow: hidden;
        }

        .reflection-panel h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .reflection-content {
            background: white;
            border-radius: 12px;
            padding: 15px;
            min-height: 150px;
            font-family: 'Segoe UI', sans-serif;
            font-size: 1em;
            line-height: 1.5;
            position: relative;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .reflection-idle {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 40px 20px;
            font-size: 1.1em;
        }

        .reflection-thinking {
            color: #667eea;
            font-weight: 500;
            animation: thinking-pulse 1.5s infinite;
            text-align: center;
            padding: 30px;
            font-size: 1.1em;
        }

        .reflection-step {
            margin-bottom: 12px;
            padding: 12px 15px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            animation: fadeInUp 0.5s ease;
            font-size: 0.95em;
            line-height: 1.4;
        }

        .reflection-final {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-left-color: #28a745;
            font-weight: 500;
            font-size: 1em;
        }

        @keyframes thinking-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .thinking-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #667eea;
            margin-right: 8px;
            animation: thinking-dot 1.4s infinite;
        }

        .thinking-indicator:nth-child(2) { animation-delay: 0.2s; }
        .thinking-indicator:nth-child(3) { animation-delay: 0.4s; }

        @keyframes thinking-dot {
            0%, 80%, 100% { opacity: 0.3; transform: scale(0.8); }
            40% { opacity: 1; transform: scale(1.2); }
        }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .input-container {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .neural-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .stat-value {
            font-weight: bold;
            color: #667eea;
        }

        .connection-status {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }

        .connected {
            background: #d4edda;
            color: #155724;
        }

        .disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 LOUNA AI - Réflexion en Temps Réel</h1>
        <p style="color: rgba(255,255,255,0.8);">Voyez comment l'agent pense et réfléchit en direct</p>
    </div>

    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-icon status-idle" id="statusIcon"></div>
            <span id="statusText">🟢 Prêt - En attente</span>
        </div>
        <div class="status-indicator">
            <span id="responseTime">⚡ Temps de réponse: --</span>
        </div>
        <div class="status-indicator">
            <span id="connectionStatus">🔗 Connecté</span>
        </div>
    </div>

    <div class="main-container">
        <div class="chat-container">
            <div class="messages" id="messages"></div>
            <div class="input-container">
                <div class="input-group">
                    <input type="text" class="message-input" id="messageInput" 
                           placeholder="Posez votre question à Louna et voyez sa réflexion..." 
                           onkeypress="handleKeyPress(event)">
                    <button class="send-button" id="sendButton" onclick="sendMessage()">
                        <span id="buttonText">Envoyer</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="reflection-container">
            <div class="reflection-panel">
                <h3>🧠 Réflexion en Direct</h3>
                <div class="reflection-content" id="reflectionContent">
                    <div class="reflection-idle">
                        💭 En attente d'une question pour commencer la réflexion...
                    </div>
                </div>
            </div>
            
            <div class="connection-status connected" id="connectionIndicator">
                🟢 Connecté au serveur
            </div>
            
            <div class="neural-stats">
                <h4>🧠 Statistiques Neurales</h4>
                <div class="stat-item">
                    <span>QI Level:</span>
                    <span class="stat-value" id="qiLevel">235</span>
                </div>
                <div class="stat-item">
                    <span>Neurones Actifs:</span>
                    <span class="stat-value" id="activeNeurons">86M</span>
                </div>
                <div class="stat-item">
                    <span>Température:</span>
                    <span class="stat-value" id="temperature">37.2°C</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let socket;
        let isProcessing = false;
        let startTime = 0;

        // Initialiser Socket.IO
        function initializeSocket() {
            socket = io();
            
            socket.on('connect', () => {
                console.log('✅ Connecté au serveur');
                updateConnectionStatus(true);
            });
            
            socket.on('disconnect', () => {
                console.log('❌ Déconnecté du serveur');
                updateConnectionStatus(false);
            });
            
            // Écouter les étapes de réflexion
            socket.on('reflection_step', (data) => {
                updateStatus(data.status, data.text);
                addReflectionStep(data.reflection);
            });
            
            // Écouter les réponses de l'agent
            socket.on('agent_response', (data) => {
                if (data.error) {
                    updateStatus('error', `❌ Erreur: ${data.message}`);
                    addMessage(`Erreur: ${data.message}`, 'bot');
                } else {
                    updateStatus('success', '✅ Réponse générée');
                    addMessage(data.message, 'bot');
                    showFinalReflection(data.reflection);
                }
                
                // Calculer le temps de réponse
                const responseTime = Date.now() - startTime;
                document.getElementById('responseTime').textContent = `⚡ Temps: ${responseTime}ms`;
                
                // Réinitialiser l'interface
                setTimeout(() => {
                    updateStatus('idle', '🟢 Prêt - En attente');
                    document.getElementById('sendButton').disabled = false;
                    document.getElementById('buttonText').textContent = 'Envoyer';
                    isProcessing = false;
                }, 1000);
            });
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('connectionIndicator');
            const statusText = document.getElementById('connectionStatus');
            
            if (connected) {
                indicator.className = 'connection-status connected';
                indicator.textContent = '🟢 Connecté au serveur';
                statusText.textContent = '🔗 Connecté';
            } else {
                indicator.className = 'connection-status disconnected';
                indicator.textContent = '🔴 Déconnecté du serveur';
                statusText.textContent = '🔗 Déconnecté';
            }
        }

        // Fonction pour mettre à jour le statut
        function updateStatus(status, text) {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            
            statusIcon.className = `status-icon ${status}`;
            statusText.textContent = text;
        }

        // Afficher l'indicateur de réflexion
        function showThinkingIndicator() {
            const reflectionContent = document.getElementById('reflectionContent');
            reflectionContent.innerHTML = `
                <div class="reflection-thinking">
                    <span class="thinking-indicator"></span>
                    <span class="thinking-indicator"></span>
                    <span class="thinking-indicator"></span>
                    <span style="margin-left: 15px;">LOUNA commence à réfléchir...</span>
                </div>
            `;
        }

        // Ajouter une étape de réflexion
        function addReflectionStep(text) {
            const reflectionContent = document.getElementById('reflectionContent');
            const step = document.createElement('div');
            step.className = 'reflection-step';
            step.textContent = text;
            
            reflectionContent.innerHTML = '';
            reflectionContent.appendChild(step);
        }

        // Afficher la réflexion finale
        function showFinalReflection(reflection) {
            const reflectionContent = document.getElementById('reflectionContent');
            const finalStep = document.createElement('div');
            finalStep.className = 'reflection-step reflection-final';
            finalStep.textContent = reflection || 'Réponse générée avec succès ! Prêt pour la prochaine question.';
            
            reflectionContent.innerHTML = '';
            reflectionContent.appendChild(finalStep);
            
            // Retour à l'état idle après 4 secondes
            setTimeout(() => {
                reflectionContent.innerHTML = `
                    <div class="reflection-idle">
                        💭 En attente d'une question pour commencer la réflexion...
                    </div>
                `;
            }, 4000);
        }

        // Fonction pour envoyer un message
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isProcessing) return;
            
            isProcessing = true;
            startTime = Date.now();
            
            // Mettre à jour l'interface
            updateStatus('thinking', '🧠 Réflexion en cours...');
            document.getElementById('sendButton').disabled = true;
            document.getElementById('buttonText').textContent = 'Traitement...';
            
            // Afficher l'indicateur de réflexion
            showThinkingIndicator();
            
            // Ajouter le message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            // Envoyer via Socket.IO
            socket.emit('user_message', {
                id: Date.now(),
                message: message,
                timestamp: Date.now()
            });
        }

        // Ajouter un message à la conversation
        function addMessage(text, sender) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isProcessing) {
                sendMessage();
            }
        }

        // Initialiser l'application
        document.addEventListener('DOMContentLoaded', () => {
            initializeSocket();
        });
    </script>
</body>
</html>

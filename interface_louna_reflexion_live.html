<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Réflexion en Temps Réel</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-weight: bold;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-idle { background: #4CAF50; }
        .status-thinking { background: #FF9800; }
        .status-memory { background: #2196F3; }
        .status-internet { background: #9C27B0; }
        .status-success { background: #4CAF50; }
        .status-error { background: #F44336; }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .main-container {
            flex: 1;
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            gap: 20px;
            padding: 20px;
        }

        .chat-container {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .reflection-container {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        /* STYLES POUR LA RÉFLEXION EN LIVE */
        .reflection-panel {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            border: 2px solid #667eea;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
            position: relative;
            overflow: hidden;
        }

        .reflection-panel h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .reflection-content {
            background: white;
            border-radius: 12px;
            padding: 15px;
            min-height: 150px;
            font-family: 'Segoe UI', sans-serif;
            font-size: 1em;
            line-height: 1.5;
            position: relative;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .reflection-idle {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 40px 20px;
            font-size: 1.1em;
        }

        .reflection-thinking {
            color: #667eea;
            font-weight: 500;
            animation: thinking-pulse 1.5s infinite;
            text-align: center;
            padding: 30px;
            font-size: 1.1em;
        }

        .reflection-step {
            margin-bottom: 12px;
            padding: 12px 15px;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #667eea;
            animation: fadeInUp 0.5s ease, neuralPulse 2s infinite;
            font-size: 0.95em;
            line-height: 1.4;
            color: #495057;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.2);
            position: relative;
            overflow: hidden;
        }

        .reflection-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
            animation: scanLine 3s infinite;
        }

        .reflection-step.cognitive {
            border-left-color: #ff6b6b;
            box-shadow: 0 2px 10px rgba(255, 107, 107, 0.2);
        }

        .reflection-step.memory {
            border-left-color: #4ecdc4;
            box-shadow: 0 2px 10px rgba(78, 205, 196, 0.2);
        }

        .reflection-step.neural {
            border-left-color: #ffe66d;
            box-shadow: 0 2px 10px rgba(255, 230, 109, 0.2);
        }

        .reflection-final {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-left-color: #28a745;
            font-weight: 500;
            font-size: 1em;
        }

        @keyframes thinking-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .thinking-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #667eea;
            margin-right: 8px;
            animation: thinking-dot 1.4s infinite;
        }

        .thinking-indicator:nth-child(2) { animation-delay: 0.2s; }
        .thinking-indicator:nth-child(3) { animation-delay: 0.4s; }

        @keyframes thinking-dot {
            0%, 80%, 100% { opacity: 0.3; transform: scale(0.8); }
            40% { opacity: 1; transform: scale(1.2); }
        }

        @keyframes neuralPulse {
            0%, 100% {
                box-shadow: 0 2px 10px rgba(102, 126, 234, 0.2);
            }
            50% {
                box-shadow: 0 2px 20px rgba(102, 126, 234, 0.4);
            }
        }

        @keyframes scanLine {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        @keyframes brainWave {
            0%, 100% {
                transform: scale(1);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
        }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .input-container {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .neural-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .stat-value {
            font-weight: bold;
            color: #667eea;
        }

        .connection-status {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }

        .connected {
            background: #d4edda;
            color: #155724;
        }

        .disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 LOUNA AI - Réflexion en Temps Réel</h1>
        <p style="color: rgba(255,255,255,0.8);">Voyez comment l'agent pense et réfléchit en direct</p>
    </div>

    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-icon status-idle" id="statusIcon"></div>
            <span id="statusText">🟢 Prêt - En attente</span>
        </div>
        <div class="status-indicator">
            <span id="responseTime">⚡ Temps de réponse: --</span>
        </div>
        <div class="status-indicator">
            <span id="connectionStatus">🔗 Connecté</span>
        </div>
    </div>

    <div class="main-container">
        <div class="chat-container">
            <div class="messages" id="messages"></div>
            <div class="input-container">
                <div class="input-group">
                    <input type="text" class="message-input" id="messageInput" 
                           placeholder="Posez votre question à Louna et voyez sa réflexion..." 
                           onkeypress="handleKeyPress(event)">
                    <button class="send-button" id="sendButton" onclick="sendMessage()">
                        <span id="buttonText">Envoyer</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="reflection-container">
            <div class="reflection-panel">
                <h3>🧠 Réflexion en Direct</h3>
                <div class="reflection-content" id="reflectionContent">
                    <div class="reflection-idle">
                        💭 En attente d'une question pour commencer la réflexion...
                    </div>
                </div>
            </div>
            
            <div class="connection-status connected" id="connectionIndicator">
                🟢 Connecté au serveur
            </div>
            
            <div class="neural-stats">
                <h4>🧠 Statistiques Neurales</h4>
                <div class="stat-item">
                    <span>QI Level:</span>
                    <span class="stat-value" id="qiLevel">235</span>
                </div>
                <div class="stat-item">
                    <span>Neurones Actifs:</span>
                    <span class="stat-value" id="activeNeurons">86M</span>
                </div>
                <div class="stat-item">
                    <span>Température:</span>
                    <span class="stat-value" id="temperature">37.2°C</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let socket;
        let isProcessing = false;
        let startTime = 0;

        // Initialiser Socket.IO
        function initializeSocket() {
            socket = io();
            
            socket.on('connect', () => {
                console.log('✅ Connecté au serveur');
                updateConnectionStatus(true);
            });
            
            socket.on('disconnect', () => {
                console.log('❌ Déconnecté du serveur');
                updateConnectionStatus(false);
            });
            
            // Écouter les étapes de réflexion avec données cognitives
            socket.on('reflection_step', (data) => {
                console.log('🧠 Étape de réflexion reçue:', data);
                updateStatus(data.status, data.text);
                addReflectionStep(data.reflection, data.status);

                // Mettre à jour les statistiques neurales si disponibles
                if (data.neural_activity) {
                    updateNeuralStats(data.neural_activity);
                }

                // Effet visuel spécial pour certaines étapes
                if (data.status === 'thinking' && data.reflection.includes('86M neurones')) {
                    showNeuralBurst();
                }
            });
            
            // Écouter les réponses de l'agent
            socket.on('agent_response', (data) => {
                if (data.error) {
                    updateStatus('error', `❌ Erreur: ${data.message}`);
                    addMessage(`Erreur: ${data.message}`, 'bot');
                } else {
                    updateStatus('success', '✅ Réponse générée');
                    addMessage(data.message, 'bot');
                    showFinalReflection(data.reflection);
                }
                
                // Calculer le temps de réponse
                const responseTime = Date.now() - startTime;
                document.getElementById('responseTime').textContent = `⚡ Temps: ${responseTime}ms`;
                
                // Réinitialiser l'interface
                setTimeout(() => {
                    updateStatus('idle', '🟢 Prêt - En attente');
                    document.getElementById('sendButton').disabled = false;
                    document.getElementById('buttonText').textContent = 'Envoyer';
                    isProcessing = false;
                }, 1000);
            });
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('connectionIndicator');
            const statusText = document.getElementById('connectionStatus');
            
            if (connected) {
                indicator.className = 'connection-status connected';
                indicator.textContent = '🟢 Connecté au serveur';
                statusText.textContent = '🔗 Connecté';
            } else {
                indicator.className = 'connection-status disconnected';
                indicator.textContent = '🔴 Déconnecté du serveur';
                statusText.textContent = '🔗 Déconnecté';
            }
        }

        // Fonction pour mettre à jour le statut
        function updateStatus(status, text) {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');

            statusIcon.className = `status-icon status-${status}`;
            statusText.textContent = text;
        }

        // Afficher l'indicateur de réflexion
        function showThinkingIndicator() {
            const reflectionContent = document.getElementById('reflectionContent');
            reflectionContent.innerHTML = `
                <div class="reflection-thinking">
                    <span class="thinking-indicator"></span>
                    <span class="thinking-indicator"></span>
                    <span class="thinking-indicator"></span>
                    <span style="margin-left: 15px;">LOUNA commence à réfléchir...</span>
                </div>
            `;
        }

        // Ajouter une étape de réflexion avec classification cognitive
        function addReflectionStep(text, status = 'thinking') {
            console.log('📝 Ajout étape de réflexion:', text);
            const reflectionContent = document.getElementById('reflectionContent');
            const step = document.createElement('div');

            // Classification cognitive basée sur le contenu
            let cognitiveClass = 'reflection-step';
            if (text.includes('🧠') || text.includes('neurones') || text.includes('QI')) {
                cognitiveClass += ' cognitive';
            } else if (text.includes('💾') || text.includes('mémoire') || text.includes('hippocampe')) {
                cognitiveClass += ' memory';
            } else if (text.includes('🧪') || text.includes('neurotransmetteurs') || text.includes('synaptiques')) {
                cognitiveClass += ' neural';
            }

            step.className = cognitiveClass;
            step.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: currentColor; animation: brainWave 1.5s infinite;"></div>
                    <div>${text}</div>
                </div>
            `;

            reflectionContent.innerHTML = '';
            reflectionContent.appendChild(step);

            // Effet sonore visuel
            step.style.transform = 'scale(0.95)';
            setTimeout(() => {
                step.style.transform = 'scale(1)';
                step.style.transition = 'transform 0.3s ease';
            }, 100);
        }

        // Afficher la réflexion finale
        function showFinalReflection(reflection) {
            const reflectionContent = document.getElementById('reflectionContent');
            const finalStep = document.createElement('div');
            finalStep.className = 'reflection-step reflection-final';
            finalStep.textContent = reflection || 'Réponse générée avec succès ! Prêt pour la prochaine question.';
            
            reflectionContent.innerHTML = '';
            reflectionContent.appendChild(finalStep);
            
            // Retour à l'état idle après 4 secondes
            setTimeout(() => {
                reflectionContent.innerHTML = `
                    <div class="reflection-idle">
                        💭 En attente d'une question pour commencer la réflexion...
                    </div>
                `;
            }, 4000);
        }

        // Fonction pour envoyer un message
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isProcessing) return;
            
            isProcessing = true;
            startTime = Date.now();
            
            // Mettre à jour l'interface
            updateStatus('thinking', '🧠 Réflexion en cours...');
            document.getElementById('sendButton').disabled = true;
            document.getElementById('buttonText').textContent = 'Traitement...';
            
            // Afficher l'indicateur de réflexion
            showThinkingIndicator();
            
            // Ajouter le message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            // Envoyer via Socket.IO
            socket.emit('user_message', {
                id: Date.now(),
                message: message,
                timestamp: Date.now()
            });
        }

        // Ajouter un message à la conversation
        function addMessage(text, sender) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isProcessing) {
                sendMessage();
            }
        }

        // Mettre à jour les statistiques neurales
        function updateNeuralStats(neuralData) {
            if (neuralData.qi_level) {
                const qiElement = document.querySelector('.stat-value');
                if (qiElement) {
                    qiElement.textContent = neuralData.qi_level;
                    qiElement.style.animation = 'brainWave 1s ease';
                }
            }
        }

        // Effet visuel d'explosion neurale
        function showNeuralBurst() {
            const reflectionPanel = document.querySelector('.reflection-panel');
            if (reflectionPanel) {
                reflectionPanel.style.animation = 'neuralPulse 0.5s ease';
                setTimeout(() => {
                    reflectionPanel.style.animation = '';
                }, 500);
            }
        }

        // Améliorer l'affichage des statistiques avec animations
        function enhanceStatsDisplay() {
            const statsElements = document.querySelectorAll('.stat-value');
            statsElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
                element.style.animation = 'fadeInUp 0.5s ease forwards';
            });
        }

        // Initialiser l'application
        document.addEventListener('DOMContentLoaded', () => {
            initializeSocket();
            enhanceStatsDisplay();
        });
    </script>
</body>
</html>

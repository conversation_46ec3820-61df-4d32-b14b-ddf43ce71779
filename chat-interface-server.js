#!/usr/bin/env node

/**
 * 🌐 SERVEUR INTERFACE CHAT TEMPS RÉEL
 * 
 * Interface web moderne pour communiquer avec l'agent DeepSeek R1 8B
 * - Socket.IO pour communication temps réel
 * - Interface moderne style ChatGPT/Claude
 * - Connexion directe avec l'agent neurologique
 * - Monitoring en temps réel du cerveau
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');
const AdvancedBrainSystem = require('./advanced-brain-system');

class ChatInterfaceServer {
    constructor(port = 3000) {
        this.port = port;
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        
        // Agent DeepSeek avec système neurologique
        this.agent = null;
        this.brainSystem = null;
        
        // Statistiques temps réel
        this.stats = {
            connections: 0,
            messages_sent: 0,
            messages_received: 0,
            brain_updates: 0,
            uptime_start: Date.now()
        };
        
        this.setupExpress();
        this.setupSocketIO();
        this.initializeAgent();
    }
    
    /**
     * Configuration Express pour servir l'interface
     */
    setupExpress() {
        // Middleware pour parser JSON
        this.app.use(express.json());

        // Servir les fichiers statiques
        this.app.use(express.static(path.join(__dirname, 'public')));

        // Route principale
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'interface_louna_avec_statut.html'));
        });

        // API pour les statistiques
        this.app.get('/api/stats', (req, res) => {
            res.json({
                ...this.stats,
                uptime: Date.now() - this.stats.uptime_start,
                agent_status: this.agent ? 'connected' : 'disconnected',
                brain_status: this.brainSystem ? 'active' : 'inactive'
            });
        });

        // API pour l'état du cerveau
        this.app.get('/api/brain', (req, res) => {
            if (this.brainSystem) {
                res.json(this.brainSystem.getBrainState());
            } else {
                res.json({ error: 'Brain system not active' });
            }
        });

        // API pour les messages de chat (NOUVELLE ROUTE)
        this.app.post('/api/chat/message', async (req, res) => {
            try {
                const { message } = req.body;

                if (!message) {
                    return res.status(400).json({
                        success: false,
                        error: 'Message requis'
                    });
                }

                if (!this.agent) {
                    return res.status(503).json({
                        success: false,
                        error: 'Agent non disponible'
                    });
                }

                console.log(`💬 Message HTTP reçu: "${message}"`);

                // Traiter le message avec l'agent
                const response = await this.agent.processMessage(message);

                // Formater la réponse pour l'interface
                const formattedResponse = {
                    success: true,
                    response: response.message || "Réponse générée",
                    reflection: response.reflection,
                    memory_used: response.memory_used || [],
                    neural_stats: this.brainSystem ? {
                        qi_level: 235,
                        temperature: 37.05,
                        total_memories: this.countTotalMemoryEntries(),
                        active_neurons: '86M'
                    } : null,
                    timestamp: Date.now()
                };

                console.log(`✅ Réponse envoyée: "${formattedResponse.response.substring(0, 100)}..."`);

                res.json(formattedResponse);

            } catch (error) {
                console.error(`❌ Erreur API chat: ${error.message}`);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Route de santé
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                agent: !!this.agent,
                brain: !!this.brainSystem,
                timestamp: Date.now()
            });
        });

        console.log('🌐 Serveur Express configuré avec API HTTP');
    }

    /**
     * Compte le total d'entrées dans la mémoire
     */
    countTotalMemoryEntries() {
        if (!this.agent || !this.agent.thermalMemoryData) return 0;

        let total = 0;
        for (const zone of Object.values(this.agent.thermalMemoryData.thermal_zones || {})) {
            if (zone.entries) {
                total += zone.entries.length;
            }
        }
        return total;
    }
    
    /**
     * Configuration Socket.IO pour communication temps réel
     */
    setupSocketIO() {
        this.io.on('connection', (socket) => {
            this.stats.connections++;
            console.log(`👤 Nouvelle connexion: ${socket.id} (Total: ${this.stats.connections})`);
            
            // Envoyer l'état initial
            socket.emit('agent_status', {
                connected: !!this.agent,
                brain_active: !!this.brainSystem,
                stats: this.stats
            });
            
            // Envoyer l'état du cerveau si disponible
            if (this.brainSystem) {
                socket.emit('brain_state', this.brainSystem.getBrainState());
            }
            
            // Écouter les messages de l'utilisateur
            socket.on('user_message', async (data) => {
                try {
                    this.stats.messages_received++;
                    console.log(`💬 Message reçu de ${socket.id}: ${data.message}`);
                    
                    // Envoyer confirmation de réception
                    socket.emit('message_received', {
                        id: data.id,
                        timestamp: Date.now()
                    });
                    
                    // Traiter le message avec l'agent
                    if (this.agent) {
                        const response = await this.processMessageWithAgent(data.message, socket);
                        
                        // Envoyer la réponse
                        socket.emit('agent_response', {
                            id: Date.now(),
                            message: response.message,
                            reflection: response.reflection,
                            memory_used: response.memory_used,
                            brain_state: this.brainSystem ? this.brainSystem.getBrainState() : null,
                            timestamp: Date.now()
                        });
                        
                        this.stats.messages_sent++;
                    } else {
                        socket.emit('agent_response', {
                            id: Date.now(),
                            message: "❌ Agent non disponible. Initialisation en cours...",
                            error: true,
                            timestamp: Date.now()
                        });
                    }
                    
                } catch (error) {
                    console.error(`❌ Erreur traitement message: ${error.message}`);
                    socket.emit('agent_response', {
                        id: Date.now(),
                        message: `❌ Erreur: ${error.message}`,
                        error: true,
                        timestamp: Date.now()
                    });
                }
            });
            
            // Demande d'état du cerveau
            socket.on('request_brain_state', () => {
                if (this.brainSystem) {
                    socket.emit('brain_state', this.brainSystem.getBrainState());
                }
            });
            
            // Déconnexion
            socket.on('disconnect', () => {
                this.stats.connections--;
                console.log(`👋 Déconnexion: ${socket.id} (Restant: ${this.stats.connections})`);
            });
        });
        
        console.log('🔌 Socket.IO configuré');
    }
    
    /**
     * Initialise l'agent DeepSeek avec système neurologique
     */
    async initializeAgent() {
        try {
            console.log('🤖 Initialisation de l\'agent DeepSeek R1 8B...');
            
            // Créer l'agent
            this.agent = new DeepSeekR1IntegratedAgent();
            const agentInitialized = await this.agent.initialize();
            
            if (!agentInitialized) {
                throw new Error('Échec initialisation agent');
            }
            
            // Récupérer le système cérébral de l'agent
            this.brainSystem = this.agent.modules.advancedBrain;
            
            if (this.brainSystem) {
                // Écouter les mises à jour du cerveau
                this.brainSystem.on('neural_heartbeat', (data) => {
                    this.stats.brain_updates++;
                    this.io.emit('brain_heartbeat', data);
                });
                
                this.brainSystem.on('neurotransmitters_updated', (nt) => {
                    this.io.emit('neurotransmitters_update', nt);
                });
                
                this.brainSystem.on('brainwaves_updated', (waves) => {
                    this.io.emit('brainwaves_update', waves);
                });
                
                this.brainSystem.on('emotions_updated', (emotions) => {
                    this.io.emit('emotions_update', emotions);
                });
                
                console.log('🧠 Système neurologique connecté à l\'interface');
            }
            
            // Notifier tous les clients connectés
            this.io.emit('agent_status', {
                connected: true,
                brain_active: !!this.brainSystem,
                stats: this.stats
            });
            
            console.log('✅ Agent DeepSeek R1 8B prêt pour l\'interface');
            
        } catch (error) {
            console.error(`❌ Erreur initialisation agent: ${error.message}`);
            
            // Notifier les clients de l'erreur
            this.io.emit('agent_status', {
                connected: false,
                brain_active: false,
                error: error.message,
                stats: this.stats
            });
        }
    }
    
    /**
     * Traite un message avec l'agent
     */
    async processMessageWithAgent(message, socket) {
        try {
            // Envoyer statut "en train de réfléchir"
            socket.emit('agent_thinking', {
                status: 'thinking',
                message: 'L\'agent réfléchit...'
            });
            
            // Traiter avec l'agent
            const response = await this.agent.processMessage(message);
            
            // Envoyer statut "terminé"
            socket.emit('agent_thinking', {
                status: 'done'
            });
            
            return response;
            
        } catch (error) {
            socket.emit('agent_thinking', {
                status: 'error',
                message: error.message
            });
            
            throw error;
        }
    }
    
    /**
     * Démarre le serveur
     */
    start() {
        this.server.listen(this.port, () => {
            console.log('\n🚀 === INTERFACE CHAT AGENT DEEPSEEK R1 8B ===');
            console.log(`🌐 Serveur démarré sur http://localhost:${this.port}`);
            console.log(`🔌 Socket.IO prêt pour connexions temps réel`);
            console.log(`🧠 Système neurologique intégré`);
            console.log(`📊 API disponible sur /api/stats et /api/brain`);
            console.log('\n✨ Interface prête pour communication avec l\'agent !');
        });
    }
    
    /**
     * Arrête le serveur proprement
     */
    stop() {
        console.log('\n🛑 Arrêt du serveur...');
        
        if (this.brainSystem) {
            this.brainSystem.shutdown();
        }
        
        this.server.close(() => {
            console.log('✅ Serveur arrêté proprement');
            process.exit(0);
        });
    }
}

// Démarrage du serveur
if (require.main === module) {
    const server = new ChatInterfaceServer(3000);
    server.start();
    
    // Gestion propre de l'arrêt
    process.on('SIGINT', () => {
        server.stop();
    });
    
    process.on('SIGTERM', () => {
        server.stop();
    });
}

module.exports = ChatInterfaceServer;

#!/usr/bin/env node

/**
 * 🖥️ SYSTÈME MPC (MODE DE CONTRÔLE DU BUREAU)
 * 
 * Système complet pour contrôler le bureau, naviguer sur Internet,
 * et effectuer des actions système pour l'agent LOUNA
 */

const { exec, spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class MPCDesktopControlSystem {
    constructor() {
        this.isActive = false;
        this.capabilities = {
            desktop_control: true,
            internet_navigation: true,
            file_management: true,
            application_control: true,
            system_monitoring: true,
            web_search: true,
            browser_automation: true
        };
        
        this.browserProcess = null;
        this.currentUrl = null;
        this.searchHistory = [];
        this.desktopActions = [];
        
        this.initializeMPC();
    }
    
    /**
     * Initialise le système MPC
     */
    async initializeMPC() {
        console.log('🖥️ === INITIALISATION SYSTÈME MPC ===\n');
        
        try {
            // Vérifier les permissions système
            await this.checkSystemPermissions();
            
            // Initialiser les modules de contrôle
            await this.initializeDesktopControl();
            await this.initializeInternetNavigation();
            await this.initializeFileManagement();
            
            this.isActive = true;
            console.log('✅ Système MPC initialisé avec succès\n');
            
            return true;
        } catch (error) {
            console.error(`❌ Erreur initialisation MPC: ${error.message}`);
            return false;
        }
    }
    
    /**
     * Vérifie les permissions système
     */
    async checkSystemPermissions() {
        console.log('🔐 Vérification des permissions système...');
        
        // Vérifier l'accès au bureau
        try {
            await this.executeCommand('osascript -e "tell application \\"System Events\\" to get name of every process"');
            console.log('✅ Permissions bureau: OK');
        } catch (error) {
            console.log('⚠️ Permissions bureau limitées');
        }
        
        // Vérifier l'accès Internet
        try {
            await axios.get('https://www.google.com', { timeout: 5000 });
            console.log('✅ Connexion Internet: OK');
        } catch (error) {
            console.log('⚠️ Connexion Internet limitée');
        }
    }
    
    /**
     * Initialise le contrôle du bureau
     */
    async initializeDesktopControl() {
        console.log('🖥️ Initialisation contrôle bureau...');

        try {
            this.desktopCommands = {
                openApplication: this.openApplication.bind(this),
                closeApplication: this.closeApplication.bind(this),
                switchApplication: this.switchApplication.bind(this),
                takeScreenshot: this.takeScreenshot.bind(this),
                clickAt: this.clickAt ? this.clickAt.bind(this) : null,
                typeText: this.typeText.bind(this),
                pressKey: this.pressKey.bind(this),
                getActiveWindow: this.getActiveWindow ? this.getActiveWindow.bind(this) : null,
                listApplications: this.listApplications.bind(this)
            };

            console.log('✅ Contrôle bureau initialisé');
        } catch (error) {
            console.log(`⚠️ Erreur initialisation contrôle bureau: ${error.message}`);
            this.desktopCommands = {};
        }
    }
    
    /**
     * Initialise la navigation Internet
     */
    async initializeInternetNavigation() {
        console.log('🌐 Initialisation navigation Internet...');
        
        this.internetCommands = {
            openBrowser: this.openBrowser.bind(this),
            navigateTo: this.navigateTo.bind(this),
            searchGoogle: this.searchGoogle.bind(this),
            searchWikipedia: this.searchWikipedia.bind(this),
            downloadFile: this.downloadFile.bind(this),
            getPageContent: this.getPageContent.bind(this),
            closeBrowser: this.closeBrowser.bind(this),
            bookmarkPage: this.bookmarkPage.bind(this),
            getSearchResults: this.getSearchResults.bind(this)
        };
        
        console.log('✅ Navigation Internet initialisée');
    }
    
    /**
     * Initialise la gestion de fichiers
     */
    async initializeFileManagement() {
        console.log('📁 Initialisation gestion fichiers...');
        
        this.fileCommands = {
            createFile: this.createFile.bind(this),
            readFile: this.readFile.bind(this),
            writeFile: this.writeFile.bind(this),
            deleteFile: this.deleteFile.bind(this),
            copyFile: this.copyFile.bind(this),
            moveFile: this.moveFile.bind(this),
            listDirectory: this.listDirectory.bind(this),
            createDirectory: this.createDirectory.bind(this),
            findFiles: this.findFiles.bind(this)
        };
        
        console.log('✅ Gestion fichiers initialisée');
    }
    
    /**
     * Exécute une commande système
     */
    async executeCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout.trim());
                }
            });
        });
    }
    
    /**
     * Ouvre une application
     */
    async openApplication(appName) {
        try {
            console.log(`🚀 Ouverture de l'application: ${appName}`);
            
            const command = `open -a "${appName}"`;
            await this.executeCommand(command);
            
            this.desktopActions.push({
                action: 'open_application',
                target: appName,
                timestamp: Date.now(),
                success: true
            });
            
            return { success: true, message: `Application ${appName} ouverte` };
        } catch (error) {
            console.error(`❌ Erreur ouverture ${appName}: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Ferme une application
     */
    async closeApplication(appName) {
        try {
            console.log(`❌ Fermeture de l'application: ${appName}`);
            
            const command = `osascript -e 'tell application "${appName}" to quit'`;
            await this.executeCommand(command);
            
            return { success: true, message: `Application ${appName} fermée` };
        } catch (error) {
            console.error(`❌ Erreur fermeture ${appName}: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Ouvre le navigateur
     */
    async openBrowser(url = 'https://www.google.com') {
        try {
            console.log(`🌐 Ouverture du navigateur: ${url}`);
            
            const command = `open "${url}"`;
            await this.executeCommand(command);
            
            this.currentUrl = url;
            
            return { success: true, message: `Navigateur ouvert sur ${url}` };
        } catch (error) {
            console.error(`❌ Erreur ouverture navigateur: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Navigue vers une URL
     */
    async navigateTo(url) {
        try {
            console.log(`🔗 Navigation vers: ${url}`);
            
            // Assurer que l'URL a un protocole
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }
            
            const command = `open "${url}"`;
            await this.executeCommand(command);
            
            this.currentUrl = url;
            
            return { success: true, message: `Navigation vers ${url}` };
        } catch (error) {
            console.error(`❌ Erreur navigation: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Effectue une recherche Google
     */
    async searchGoogle(query) {
        try {
            console.log(`🔍 Recherche Google: ${query}`);
            
            const encodedQuery = encodeURIComponent(query);
            const searchUrl = `https://www.google.com/search?q=${encodedQuery}`;
            
            await this.navigateTo(searchUrl);
            
            this.searchHistory.push({
                query: query,
                url: searchUrl,
                timestamp: Date.now(),
                engine: 'google'
            });
            
            return { 
                success: true, 
                message: `Recherche Google effectuée: ${query}`,
                url: searchUrl
            };
        } catch (error) {
            console.error(`❌ Erreur recherche Google: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Effectue une recherche Wikipedia
     */
    async searchWikipedia(query) {
        try {
            console.log(`📚 Recherche Wikipedia: ${query}`);
            
            const encodedQuery = encodeURIComponent(query);
            const searchUrl = `https://fr.wikipedia.org/wiki/Special:Search?search=${encodedQuery}`;
            
            await this.navigateTo(searchUrl);
            
            this.searchHistory.push({
                query: query,
                url: searchUrl,
                timestamp: Date.now(),
                engine: 'wikipedia'
            });
            
            return { 
                success: true, 
                message: `Recherche Wikipedia effectuée: ${query}`,
                url: searchUrl
            };
        } catch (error) {
            console.error(`❌ Erreur recherche Wikipedia: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Prend une capture d'écran
     */
    async takeScreenshot(filename = null) {
        try {
            if (!filename) {
                filename = `screenshot_${Date.now()}.png`;
            }
            
            console.log(`📸 Capture d'écran: ${filename}`);
            
            const command = `screencapture "${filename}"`;
            await this.executeCommand(command);
            
            return { 
                success: true, 
                message: `Capture d'écran sauvegardée: ${filename}`,
                filename: filename
            };
        } catch (error) {
            console.error(`❌ Erreur capture d'écran: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Liste les applications en cours d'exécution
     */
    async listApplications() {
        try {
            console.log('📋 Liste des applications...');
            
            const command = 'osascript -e "tell application \\"System Events\\" to get name of every process whose background only is false"';
            const result = await this.executeCommand(command);
            
            const apps = result.split(', ').map(app => app.trim());
            
            return { 
                success: true, 
                applications: apps,
                count: apps.length
            };
        } catch (error) {
            console.error(`❌ Erreur liste applications: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Tape du texte
     */
    async typeText(text) {
        try {
            console.log(`⌨️ Saisie de texte: ${text.substring(0, 50)}...`);
            
            const command = `osascript -e 'tell application "System Events" to keystroke "${text}"'`;
            await this.executeCommand(command);
            
            return { success: true, message: `Texte saisi: ${text}` };
        } catch (error) {
            console.error(`❌ Erreur saisie texte: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Appuie sur une touche
     */
    async pressKey(key) {
        try {
            console.log(`🔑 Appui sur la touche: ${key}`);
            
            const command = `osascript -e 'tell application "System Events" to key code ${this.getKeyCode(key)}'`;
            await this.executeCommand(command);
            
            return { success: true, message: `Touche pressée: ${key}` };
        } catch (error) {
            console.error(`❌ Erreur appui touche: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Obtient le code d'une touche
     */
    getKeyCode(key) {
        const keyCodes = {
            'enter': 36,
            'return': 36,
            'space': 49,
            'tab': 48,
            'escape': 53,
            'delete': 51,
            'backspace': 51,
            'up': 126,
            'down': 125,
            'left': 123,
            'right': 124,
            'cmd': 55,
            'shift': 56,
            'option': 58,
            'control': 59
        };
        
        return keyCodes[key.toLowerCase()] || 49; // Default to space
    }
    
    /**
     * Traite une commande MPC
     */
    async processCommand(command, params = {}) {
        if (!this.isActive) {
            return { success: false, error: 'Système MPC non actif' };
        }
        
        console.log(`🎮 Commande MPC: ${command}`);
        
        try {
            switch (command.toLowerCase()) {
                case 'open_app':
                case 'open_application':
                    return await this.openApplication(params.name || params.app);
                    
                case 'close_app':
                case 'close_application':
                    return await this.closeApplication(params.name || params.app);
                    
                case 'open_browser':
                    return await this.openBrowser(params.url);
                    
                case 'navigate':
                case 'navigate_to':
                    return await this.navigateTo(params.url);
                    
                case 'search_google':
                case 'google':
                    return await this.searchGoogle(params.query);
                    
                case 'search_wikipedia':
                case 'wikipedia':
                    return await this.searchWikipedia(params.query);
                    
                case 'screenshot':
                case 'capture':
                    return await this.takeScreenshot(params.filename);
                    
                case 'list_apps':
                case 'list_applications':
                    return await this.listApplications();
                    
                case 'type':
                case 'type_text':
                    return await this.typeText(params.text);
                    
                case 'press_key':
                case 'key':
                    return await this.pressKey(params.key);
                    
                default:
                    return { 
                        success: false, 
                        error: `Commande MPC inconnue: ${command}`,
                        available_commands: Object.keys(this.getAvailableCommands())
                    };
            }
        } catch (error) {
            console.error(`❌ Erreur commande MPC: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Obtient les commandes disponibles
     */
    getAvailableCommands() {
        return {
            desktop: Object.keys(this.desktopCommands || {}),
            internet: Object.keys(this.internetCommands || {}),
            files: Object.keys(this.fileCommands || {})
        };
    }
    
    /**
     * Obtient le statut du système MPC
     */
    getStatus() {
        return {
            active: this.isActive,
            capabilities: this.capabilities,
            current_url: this.currentUrl,
            search_history_count: this.searchHistory.length,
            desktop_actions_count: this.desktopActions.length,
            available_commands: this.getAvailableCommands()
        };
    }
}

module.exports = MPCDesktopControlSystem;

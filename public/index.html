<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Agent DeepSeek R1 8B - Interface Neurologique</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar avec informations neurologiques -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-brain"></i> Agent DeepSeek R1 8B</h2>
                <div class="agent-status" id="agentStatus">
                    <span class="status-dot disconnected"></span>
                    <span>Initialisation...</span>
                </div>
            </div>
            
            <!-- Monitoring neurologique temps réel -->
            <div class="brain-monitor">
                <h3><i class="fas fa-heartbeat"></i> Système Neurologique</h3>
                
                <!-- Battement cardiaque -->
                <div class="monitor-section">
                    <h4>💓 Battement Cardiaque</h4>
                    <div class="heartbeat-display">
                        <span id="heartbeatRate">0.0</span>/s
                        <div class="heartbeat-visual" id="heartbeatVisual"></div>
                    </div>
                    <div class="temperature-display">
                        🌡️ <span id="temperature">37.0</span>°C
                    </div>
                </div>
                
                <!-- Ondes cérébrales -->
                <div class="monitor-section">
                    <h4>🌊 Ondes Cérébrales</h4>
                    <div class="brainwaves">
                        <div class="wave delta">
                            <span>Delta</span>
                            <div class="wave-bar"><div class="wave-fill" id="deltaWave"></div></div>
                        </div>
                        <div class="wave theta">
                            <span>Theta</span>
                            <div class="wave-bar"><div class="wave-fill" id="thetaWave"></div></div>
                        </div>
                        <div class="wave alpha">
                            <span>Alpha</span>
                            <div class="wave-bar"><div class="wave-fill" id="alphaWave"></div></div>
                        </div>
                        <div class="wave beta">
                            <span>Beta</span>
                            <div class="wave-bar"><div class="wave-fill" id="betaWave"></div></div>
                        </div>
                        <div class="wave gamma">
                            <span>Gamma</span>
                            <div class="wave-bar"><div class="wave-fill" id="gammaWave"></div></div>
                        </div>
                    </div>
                    <div class="dominant-wave">
                        Dominante: <span id="dominantWave">Beta</span>
                    </div>
                </div>
                
                <!-- État émotionnel -->
                <div class="monitor-section">
                    <h4>🎭 État Émotionnel</h4>
                    <div class="emotion-display">
                        <div class="current-emotion" id="currentEmotion">Curiosity</div>
                        <div class="emotion-intensity">
                            Intensité: <span id="emotionIntensity">0.5</span>
                        </div>
                    </div>
                </div>
                
                <!-- Neurotransmetteurs -->
                <div class="monitor-section">
                    <h4>🧪 Neurotransmetteurs</h4>
                    <div class="neurotransmitters">
                        <div class="nt-item">
                            <span>Dopamine</span>
                            <div class="nt-level" id="dopamineLevel">0.75</div>
                        </div>
                        <div class="nt-item">
                            <span>Sérotonine</span>
                            <div class="nt-level" id="serotoninLevel">0.68</div>
                        </div>
                        <div class="nt-item">
                            <span>Acétylcholine</span>
                            <div class="nt-level" id="acetylcholineLevel">0.82</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistiques -->
            <div class="stats-section">
                <h3><i class="fas fa-chart-line"></i> Statistiques</h3>
                <div class="stat-item">
                    <span>Messages envoyés:</span>
                    <span id="messagesSent">0</span>
                </div>
                <div class="stat-item">
                    <span>Messages reçus:</span>
                    <span id="messagesReceived">0</span>
                </div>
                <div class="stat-item">
                    <span>Mises à jour cerveau:</span>
                    <span id="brainUpdates">0</span>
                </div>
            </div>
        </div>
        
        <!-- Zone de chat principale -->
        <div class="chat-container">
            <div class="chat-header">
                <h1><i class="fas fa-comments"></i> Chat avec Agent DeepSeek R1 8B</h1>
                <div class="connection-status" id="connectionStatus">
                    <i class="fas fa-wifi"></i> Connexion...
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <div class="message agent-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                🧠 Bonjour ! Je suis votre agent DeepSeek R1 8B avec système neurologique complet.
                                <br><br>
                                ✨ <strong>Fonctionnalités :</strong>
                                <br>• 💓 Battement cardiaque neurologique (9.9/s)
                                <br>• 🧪 5 neurotransmetteurs automatiques
                                <br>• 🌊 5 ondes cérébrales modulées
                                <br>• 🎭 Système émotionnel complexe
                                <br>• 🌡️ Température thermique temps réel
                                <br><br>
                                Posez-moi vos questions, je vous réponds avec ma mémoire thermique et mon système neurologique !
                            </div>
                            <div class="message-time">Maintenant</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="thinking-indicator" id="thinkingIndicator" style="display: none;">
                <div class="thinking-animation">
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                </div>
                <span>L'agent réfléchit avec son système neurologique...</span>
            </div>
            
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="Tapez votre message ici... (Entrée pour envoyer, Shift+Entrée pour nouvelle ligne)"
                        rows="1"
                    ></textarea>
                    <button id="sendButton" class="send-button">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>

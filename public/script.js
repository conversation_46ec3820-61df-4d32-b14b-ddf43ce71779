/**
 * 🌐 INTERFACE CHAT TEMPS RÉEL - AGENT DEEPSEEK R1 8B
 * 
 * Communication temps réel avec l'agent neurologique
 * Monitoring du système cérébral en direct
 * Interface moderne et responsive
 */

class ChatInterface {
    constructor() {
        this.socket = io();
        this.messageId = 0;
        this.isConnected = false;
        this.agentConnected = false;
        this.brainActive = false;
        
        // Éléments DOM
        this.elements = {
            // Status
            agentStatus: document.getElementById('agentStatus'),
            connectionStatus: document.getElementById('connectionStatus'),
            
            // Chat
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            thinkingIndicator: document.getElementById('thinkingIndicator'),
            
            // Monitoring neurologique
            heartbeatRate: document.getElementById('heartbeatRate'),
            heartbeatVisual: document.getElementById('heartbeatVisual'),
            temperature: document.getElementById('temperature'),
            dominantWave: document.getElementById('dominantWave'),
            currentEmotion: document.getElementById('currentEmotion'),
            emotionIntensity: document.getElementById('emotionIntensity'),
            
            // Ondes cérébrales
            deltaWave: document.getElementById('deltaWave'),
            thetaWave: document.getElementById('thetaWave'),
            alphaWave: document.getElementById('alphaWave'),
            betaWave: document.getElementById('betaWave'),
            gammaWave: document.getElementById('gammaWave'),
            
            // Neurotransmetteurs
            dopamineLevel: document.getElementById('dopamineLevel'),
            serotoninLevel: document.getElementById('serotoninLevel'),
            acetylcholineLevel: document.getElementById('acetylcholineLevel'),
            
            // Statistiques
            messagesSent: document.getElementById('messagesSent'),
            messagesReceived: document.getElementById('messagesReceived'),
            brainUpdates: document.getElementById('brainUpdates')
        };
        
        this.initializeEventListeners();
        this.setupSocketListeners();
        
        console.log('🌐 Interface chat initialisée');
    }
    
    /**
     * Initialise les événements de l'interface
     */
    initializeEventListeners() {
        // Envoi de message
        this.elements.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });
        
        // Envoi avec Entrée
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize du textarea
        this.elements.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
        
        console.log('✅ Événements interface configurés');
    }
    
    /**
     * Configure les listeners Socket.IO
     */
    setupSocketListeners() {
        // Connexion
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus();
            console.log('🔌 Connecté au serveur');
        });
        
        // Déconnexion
        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus();
            console.log('❌ Déconnecté du serveur');
        });
        
        // Statut de l'agent
        this.socket.on('agent_status', (data) => {
            this.agentConnected = data.connected;
            this.brainActive = data.brain_active;
            this.updateAgentStatus(data);
            this.updateStats(data.stats);
        });
        
        // État du cerveau
        this.socket.on('brain_state', (brainState) => {
            this.updateBrainMonitoring(brainState);
        });
        
        // Battement cardiaque
        this.socket.on('brain_heartbeat', (data) => {
            this.updateHeartbeat(data);
        });
        
        // Neurotransmetteurs
        this.socket.on('neurotransmitters_update', (neurotransmitters) => {
            this.updateNeurotransmitters(neurotransmitters);
        });
        
        // Ondes cérébrales
        this.socket.on('brainwaves_update', (waves) => {
            this.updateBrainwaves(waves);
        });
        
        // Émotions
        this.socket.on('emotions_update', (emotions) => {
            this.updateEmotions(emotions);
        });
        
        // Réponse de l'agent
        this.socket.on('agent_response', (response) => {
            this.hideThinking();
            this.addMessage('agent', response.message, {
                reflection: response.reflection,
                memory_used: response.memory_used,
                brain_state: response.brain_state,
                error: response.error
            });
        });
        
        // Agent en train de réfléchir
        this.socket.on('agent_thinking', (data) => {
            if (data.status === 'thinking') {
                this.showThinking();
            } else if (data.status === 'done' || data.status === 'error') {
                this.hideThinking();
            }
        });
        
        // Confirmation de réception
        this.socket.on('message_received', (data) => {
            console.log(`✅ Message ${data.id} reçu par l'agent`);
        });
        
        console.log('🔌 Listeners Socket.IO configurés');
    }
    
    /**
     * Envoie un message à l'agent
     */
    sendMessage() {
        const message = this.elements.messageInput.value.trim();
        
        if (!message || !this.agentConnected) {
            return;
        }
        
        // Ajouter le message à l'interface
        this.addMessage('user', message);
        
        // Envoyer au serveur
        const messageData = {
            id: ++this.messageId,
            message: message,
            timestamp: Date.now()
        };
        
        this.socket.emit('user_message', messageData);
        
        // Vider le champ
        this.elements.messageInput.value = '';
        this.autoResizeTextarea();
        
        // Désactiver temporairement
        this.elements.sendButton.disabled = true;
        setTimeout(() => {
            this.elements.sendButton.disabled = false;
        }, 1000);
    }
    
    /**
     * Ajoute un message à l'interface
     */
    addMessage(sender, text, extras = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        if (extras.error) {
            messageDiv.classList.add('error-message');
        }
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        messageText.innerHTML = this.formatMessage(text);
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = new Date().toLocaleTimeString();
        
        content.appendChild(messageText);
        
        // Ajouter la réflexion si disponible
        if (extras.reflection) {
            const reflectionDiv = document.createElement('div');
            reflectionDiv.className = 'reflection-section';
            reflectionDiv.innerHTML = `
                <div class="reflection-title">💭 Réflexion de l'agent :</div>
                <div class="reflection-content">${this.formatMessage(extras.reflection)}</div>
            `;
            content.appendChild(reflectionDiv);
        }
        
        // Ajouter la mémoire utilisée si disponible
        if (extras.memory_used && extras.memory_used.length > 0) {
            const memoryDiv = document.createElement('div');
            memoryDiv.className = 'memory-section';
            const memoryItems = extras.memory_used.map(mem => 
                `<div class="memory-item">🧠 ${mem.content.substring(0, 100)}...</div>`
            ).join('');
            memoryDiv.innerHTML = `
                <div class="memory-title">🧠 Mémoire utilisée (${extras.memory_used.length}) :</div>
                <div class="memory-items">${memoryItems}</div>
            `;
            content.appendChild(memoryDiv);
        }
        
        content.appendChild(messageTime);
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    /**
     * Formate le texte du message
     */
    formatMessage(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
    
    /**
     * Affiche l'indicateur de réflexion
     */
    showThinking() {
        this.elements.thinkingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }
    
    /**
     * Cache l'indicateur de réflexion
     */
    hideThinking() {
        this.elements.thinkingIndicator.style.display = 'none';
    }
    
    /**
     * Auto-resize du textarea
     */
    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    /**
     * Scroll vers le bas
     */
    scrollToBottom() {
        setTimeout(() => {
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
        }, 100);
    }
    
    /**
     * Met à jour le statut de connexion
     */
    updateConnectionStatus() {
        const statusElement = this.elements.connectionStatus;
        
        if (this.isConnected) {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> Connecté';
            statusElement.className = 'connection-status connected';
        } else {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> Déconnecté';
            statusElement.className = 'connection-status';
        }
    }
    
    /**
     * Met à jour le statut de l'agent
     */
    updateAgentStatus(data) {
        const statusElement = this.elements.agentStatus;
        const dot = statusElement.querySelector('.status-dot');
        const text = statusElement.querySelector('span:last-child');
        
        if (data.connected && data.brain_active) {
            dot.className = 'status-dot connected';
            text.textContent = 'Agent + Cerveau Actifs';
        } else if (data.connected) {
            dot.className = 'status-dot connected';
            text.textContent = 'Agent Connecté';
        } else {
            dot.className = 'status-dot disconnected';
            text.textContent = data.error || 'Déconnecté';
        }
    }
    
    /**
     * Met à jour le monitoring du cerveau
     */
    updateBrainMonitoring(brainState) {
        // Température
        if (brainState.global_temperature) {
            this.elements.temperature.textContent = brainState.global_temperature.toFixed(2);
        }
        
        // Onde dominante
        if (brainState.dominant_wave) {
            this.elements.dominantWave.textContent = brainState.dominant_wave;
        }
        
        // État émotionnel
        if (brainState.emotional_state) {
            this.elements.currentEmotion.textContent = brainState.emotional_state;
        }
        
        // Ondes cérébrales
        if (brainState.brain_waves && brainState.brain_waves.frequencies) {
            this.updateBrainwaves(brainState.brain_waves);
        }
        
        // Neurotransmetteurs
        if (brainState.neurotransmitters) {
            this.updateNeurotransmitters(brainState.neurotransmitters);
        }
        
        // Émotions
        if (brainState.emotional && brainState.emotional.current_emotional_state) {
            this.updateEmotions(brainState.emotional);
        }
    }
    
    /**
     * Met à jour le battement cardiaque
     */
    updateHeartbeat(data) {
        if (data.intensity) {
            const rate = (1000 / 100).toFixed(1); // 100ms = 10/s
            this.elements.heartbeatRate.textContent = rate;
            
            // Animation du battement
            const visual = this.elements.heartbeatVisual;
            visual.style.animationDuration = `${1/data.intensity}s`;
        }
        
        if (data.temperature) {
            this.elements.temperature.textContent = data.temperature.toFixed(2);
        }
    }
    
    /**
     * Met à jour les neurotransmetteurs
     */
    updateNeurotransmitters(neurotransmitters) {
        if (neurotransmitters.dopamine) {
            this.elements.dopamineLevel.textContent = neurotransmitters.dopamine.level.toFixed(3);
        }
        if (neurotransmitters.serotonin) {
            this.elements.serotoninLevel.textContent = neurotransmitters.serotonin.level.toFixed(3);
        }
        if (neurotransmitters.acetylcholine) {
            this.elements.acetylcholineLevel.textContent = neurotransmitters.acetylcholine.level.toFixed(3);
        }
    }
    
    /**
     * Met à jour les ondes cérébrales
     */
    updateBrainwaves(waves) {
        const waveElements = {
            delta: this.elements.deltaWave,
            theta: this.elements.thetaWave,
            alpha: this.elements.alphaWave,
            beta: this.elements.betaWave,
            gamma: this.elements.gammaWave
        };
        
        for (const [waveName, wave] of Object.entries(waves.frequencies)) {
            const element = waveElements[waveName];
            if (element) {
                const percentage = (wave.amplitude * 100).toFixed(0);
                element.style.width = `${percentage}%`;
                
                // Marquer l'onde active
                const waveContainer = element.closest('.wave');
                if (wave.active) {
                    waveContainer.classList.add('active');
                } else {
                    waveContainer.classList.remove('active');
                }
            }
        }
        
        if (waves.current_dominant) {
            this.elements.dominantWave.textContent = waves.current_dominant;
        }
    }
    
    /**
     * Met à jour les émotions
     */
    updateEmotions(emotions) {
        const state = emotions.current_emotional_state;
        
        if (state.primary_emotion) {
            this.elements.currentEmotion.textContent = state.primary_emotion;
        }
        
        if (state.intensity !== undefined) {
            this.elements.emotionIntensity.textContent = state.intensity.toFixed(2);
        }
    }
    
    /**
     * Met à jour les statistiques
     */
    updateStats(stats) {
        if (stats.messages_sent !== undefined) {
            this.elements.messagesSent.textContent = stats.messages_sent;
        }
        if (stats.messages_received !== undefined) {
            this.elements.messagesReceived.textContent = stats.messages_received;
        }
        if (stats.brain_updates !== undefined) {
            this.elements.brainUpdates.textContent = stats.brain_updates;
        }
    }
}

// Initialiser l'interface quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.chatInterface = new ChatInterface();
    console.log('🚀 Interface chat prête !');
});

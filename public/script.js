/**
 * 🌐 INTERFACE CHAT TEMPS RÉEL - AGENT DEEPSEEK R1 8B
 * 
 * Communication temps réel avec l'agent neurologique
 * Monitoring du système cérébral en direct
 * Interface moderne et responsive
 */

class ChatInterface {
    constructor() {
        console.log('🔌 Initialisation Socket.IO...');
        this.socket = io('http://localhost:3000');
        this.messageId = 0;
        this.isConnected = false;
        this.agentConnected = false;
        this.brainActive = false;
        
        // Éléments DOM
        this.elements = {
            // Status
            agentStatus: document.getElementById('agentStatus'),
            connectionStatus: document.getElementById('connectionStatus'),

            // Chat
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            thinkingIndicator: document.getElementById('thinkingIndicator'),

            // Système cognitif KYBER
            cognitiveSystem: document.getElementById('cognitiveSystem'),
            cognitiveSteps: document.getElementById('cognitiveSteps'),
            neuronNetwork: document.getElementById('neuronNetwork'),
            kyberBoost: document.getElementById('kyberBoost'),

            // Contrôles audio
            micButton: document.getElementById('micButton'),
            speakerButton: document.getElementById('speakerButton'),
            audioStatus: document.getElementById('audioStatus'),
            audioIndicator: document.getElementById('audioIndicator'),

            // Contrôles documents
            documentButton: document.getElementById('documentButton'),
            clearButton: document.getElementById('clearButton'),
            documentInfo: document.getElementById('documentInfo'),
            documentStats: document.getElementById('documentStats'),

            // Contrôles KYBER
            kyberToggle: document.getElementById('kyberToggle'),
            cognitiveToggle: document.getElementById('cognitiveToggle'),

            // Monitoring neurologique
            heartbeatRate: document.getElementById('heartbeatRate'),
            heartbeatVisual: document.getElementById('heartbeatVisual'),
            temperature: document.getElementById('temperature'),
            dominantWave: document.getElementById('dominantWave'),
            currentEmotion: document.getElementById('currentEmotion'),
            emotionIntensity: document.getElementById('emotionIntensity'),

            // Ondes cérébrales
            deltaWave: document.getElementById('deltaWave'),
            thetaWave: document.getElementById('thetaWave'),
            alphaWave: document.getElementById('alphaWave'),
            betaWave: document.getElementById('betaWave'),
            gammaWave: document.getElementById('gammaWave'),

            // Neurotransmetteurs
            dopamineLevel: document.getElementById('dopamineLevel'),
            serotoninLevel: document.getElementById('serotoninLevel'),
            acetylcholineLevel: document.getElementById('acetylcholineLevel'),

            // Statistiques
            messagesSent: document.getElementById('messagesSent'),
            messagesReceived: document.getElementById('messagesReceived'),
            brainUpdates: document.getElementById('brainUpdates')
        };

        // États des fonctionnalités
        this.audioState = {
            micActive: false,
            speakerActive: true,
            recognition: null,
            synthesis: null
        };

        this.kyberState = {
            active: true,
            cognitiveVisible: false,
            currentBoost: 166
        };

        this.documentState = {
            hasDocument: false,
            documentLength: 0,
            documentMode: false
        };
        
        this.initializeEventListeners();
        this.setupSocketListeners();
        
        console.log('🌐 Interface chat initialisée');
    }
    
    /**
     * Initialise les événements de l'interface
     */
    initializeEventListeners() {
        // Envoi de message
        this.elements.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });
        
        // Envoi avec Entrée
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize du textarea
        this.elements.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });

        // Contrôles audio
        this.elements.micButton.addEventListener('click', () => {
            this.toggleMicrophone();
        });

        this.elements.speakerButton.addEventListener('click', () => {
            this.toggleSpeaker();
        });

        // Contrôles KYBER
        this.elements.kyberToggle.addEventListener('click', () => {
            this.toggleKyber();
        });

        this.elements.cognitiveToggle.addEventListener('click', () => {
            this.toggleCognitive();
        });

        // Contrôles documents
        this.elements.documentButton.addEventListener('click', () => {
            this.toggleDocumentMode();
        });

        this.elements.clearButton.addEventListener('click', () => {
            this.clearDocument();
        });

        // Surveillance du contenu pour détecter les documents
        this.elements.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
            this.updateDocumentStats();
        });

        this.elements.messageInput.addEventListener('paste', (e) => {
            setTimeout(() => {
                this.detectDocument();
            }, 100);
        });

        // Initialiser les systèmes
        this.initializeAudio();
        this.initializeKyber();
        this.initializeDocuments();

        console.log('✅ Événements interface configurés');
    }
    
    /**
     * Configure les listeners Socket.IO
     */
    setupSocketListeners() {
        // Connexion
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus();
            console.log('✅ Connecté au serveur Socket.IO');
            console.log('🆔 Socket ID:', this.socket.id);
        });
        
        // Déconnexion
        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus();
            console.log('❌ Déconnecté du serveur');
        });
        
        // Statut de l'agent
        this.socket.on('agent_status', (data) => {
            this.agentConnected = data.connected;
            this.brainActive = data.brain_active;
            this.updateAgentStatus(data);
            this.updateStats(data.stats);
        });
        
        // État du cerveau
        this.socket.on('brain_state', (brainState) => {
            this.updateBrainMonitoring(brainState);
        });
        
        // Battement cardiaque
        this.socket.on('brain_heartbeat', (data) => {
            this.updateHeartbeat(data);
        });
        
        // Neurotransmetteurs
        this.socket.on('neurotransmitters_update', (neurotransmitters) => {
            this.updateNeurotransmitters(neurotransmitters);
        });
        
        // Ondes cérébrales
        this.socket.on('brainwaves_update', (waves) => {
            this.updateBrainwaves(waves);
        });
        
        // Émotions
        this.socket.on('emotions_update', (emotions) => {
            this.updateEmotions(emotions);
        });
        
        // Réponse de l'agent
        this.socket.on('agent_response', (response) => {
            this.hideThinking();
            this.addMessage('agent', response.message, {
                reflection: response.reflection,
                memory_used: response.memory_used,
                brain_state: response.brain_state,
                error: response.error
            });

            // Synthèse vocale si activée
            this.speakMessage(response.message);
        });
        
        // Agent en train de réfléchir
        this.socket.on('agent_thinking', (data) => {
            if (data.status === 'thinking') {
                this.showThinking();
            } else if (data.status === 'done' || data.status === 'error') {
                this.hideThinking();
            }
        });
        
        // Étapes de réflexion KYBER
        this.socket.on('reflection_step', (step) => {
            this.addCognitiveStep(step);
        });

        // Confirmation de réception
        this.socket.on('message_received', (data) => {
            console.log(`✅ Message ${data.id} reçu par l'agent`);
        });

        console.log('🔌 Listeners Socket.IO configurés');
    }
    
    /**
     * Envoie un message à l'agent
     */
    sendMessage() {
        const message = this.elements.messageInput.value.trim();

        console.log('📤 Envoi message:', message);
        console.log('🔌 Socket connecté:', this.socket && this.socket.connected);
        console.log('🤖 Agent connecté:', this.agentConnected);

        if (!message) {
            console.log('❌ Message vide');
            return;
        }

        if (!this.socket || !this.socket.connected) {
            console.error('❌ Socket non connecté !');
            this.addMessage('system', 'Erreur: Connexion au serveur perdue');
            return;
        }
        
        // Ajouter le message à l'interface
        this.addMessage('user', message);
        
        // Envoyer au serveur
        const messageData = {
            id: ++this.messageId,
            message: message,
            timestamp: Date.now()
        };

        console.log('📡 Émission user_message:', messageData);
        this.socket.emit('user_message', messageData);
        
        // Vider le champ
        this.elements.messageInput.value = '';
        this.autoResizeTextarea();
        
        // Désactiver temporairement
        this.elements.sendButton.disabled = true;
        setTimeout(() => {
            this.elements.sendButton.disabled = false;
        }, 1000);
    }
    
    /**
     * Ajoute un message à l'interface
     */
    addMessage(sender, text, extras = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        if (extras.error) {
            messageDiv.classList.add('error-message');
        }
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        messageText.innerHTML = this.formatMessage(text);
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = new Date().toLocaleTimeString();
        
        content.appendChild(messageText);
        
        // Ajouter la réflexion si disponible
        if (extras.reflection) {
            const reflectionDiv = document.createElement('div');
            reflectionDiv.className = 'reflection-section';
            reflectionDiv.innerHTML = `
                <div class="reflection-title">💭 Réflexion de l'agent :</div>
                <div class="reflection-content">${this.formatMessage(extras.reflection)}</div>
            `;
            content.appendChild(reflectionDiv);
        }
        
        // Ajouter la mémoire utilisée si disponible
        if (extras.memory_used && extras.memory_used.length > 0) {
            const memoryDiv = document.createElement('div');
            memoryDiv.className = 'memory-section';
            const memoryItems = extras.memory_used.map(mem => 
                `<div class="memory-item">🧠 ${mem.content.substring(0, 100)}...</div>`
            ).join('');
            memoryDiv.innerHTML = `
                <div class="memory-title">🧠 Mémoire utilisée (${extras.memory_used.length}) :</div>
                <div class="memory-items">${memoryItems}</div>
            `;
            content.appendChild(memoryDiv);
        }
        
        content.appendChild(messageTime);
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    /**
     * Formate le texte du message
     */
    formatMessage(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
    
    /**
     * Affiche l'indicateur de réflexion
     */
    showThinking() {
        this.elements.thinkingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }
    
    /**
     * Cache l'indicateur de réflexion
     */
    hideThinking() {
        this.elements.thinkingIndicator.style.display = 'none';
    }
    
    /**
     * Auto-resize du textarea
     */
    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    /**
     * Scroll vers le bas
     */
    scrollToBottom() {
        setTimeout(() => {
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
        }, 100);
    }
    
    /**
     * Met à jour le statut de connexion
     */
    updateConnectionStatus() {
        const statusElement = this.elements.connectionStatus;
        
        if (this.isConnected) {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> Connecté';
            statusElement.className = 'connection-status connected';
        } else {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> Déconnecté';
            statusElement.className = 'connection-status';
        }
    }
    
    /**
     * Met à jour le statut de l'agent
     */
    updateAgentStatus(data) {
        const statusElement = this.elements.agentStatus;
        const dot = statusElement.querySelector('.status-dot');
        const text = statusElement.querySelector('span:last-child');
        
        if (data.connected && data.brain_active) {
            dot.className = 'status-dot connected';
            text.textContent = 'Agent + Cerveau Actifs';
        } else if (data.connected) {
            dot.className = 'status-dot connected';
            text.textContent = 'Agent Connecté';
        } else {
            dot.className = 'status-dot disconnected';
            text.textContent = data.error || 'Déconnecté';
        }
    }
    
    /**
     * Met à jour le monitoring du cerveau
     */
    updateBrainMonitoring(brainState) {
        // Température
        if (brainState.global_temperature) {
            this.elements.temperature.textContent = brainState.global_temperature.toFixed(2);
        }
        
        // Onde dominante
        if (brainState.dominant_wave) {
            this.elements.dominantWave.textContent = brainState.dominant_wave;
        }
        
        // État émotionnel
        if (brainState.emotional_state) {
            this.elements.currentEmotion.textContent = brainState.emotional_state;
        }
        
        // Ondes cérébrales
        if (brainState.brain_waves && brainState.brain_waves.frequencies) {
            this.updateBrainwaves(brainState.brain_waves);
        }
        
        // Neurotransmetteurs
        if (brainState.neurotransmitters) {
            this.updateNeurotransmitters(brainState.neurotransmitters);
        }
        
        // Émotions
        if (brainState.emotional && brainState.emotional.current_emotional_state) {
            this.updateEmotions(brainState.emotional);
        }
    }
    
    /**
     * Met à jour le battement cardiaque
     */
    updateHeartbeat(data) {
        if (data.intensity) {
            const rate = (1000 / 100).toFixed(1); // 100ms = 10/s
            this.elements.heartbeatRate.textContent = rate;
            
            // Animation du battement
            const visual = this.elements.heartbeatVisual;
            visual.style.animationDuration = `${1/data.intensity}s`;
        }
        
        if (data.temperature) {
            this.elements.temperature.textContent = data.temperature.toFixed(2);
        }
    }
    
    /**
     * Met à jour les neurotransmetteurs
     */
    updateNeurotransmitters(neurotransmitters) {
        if (neurotransmitters.dopamine) {
            this.elements.dopamineLevel.textContent = neurotransmitters.dopamine.level.toFixed(3);
        }
        if (neurotransmitters.serotonin) {
            this.elements.serotoninLevel.textContent = neurotransmitters.serotonin.level.toFixed(3);
        }
        if (neurotransmitters.acetylcholine) {
            this.elements.acetylcholineLevel.textContent = neurotransmitters.acetylcholine.level.toFixed(3);
        }
    }
    
    /**
     * Met à jour les ondes cérébrales
     */
    updateBrainwaves(waves) {
        const waveElements = {
            delta: this.elements.deltaWave,
            theta: this.elements.thetaWave,
            alpha: this.elements.alphaWave,
            beta: this.elements.betaWave,
            gamma: this.elements.gammaWave
        };
        
        for (const [waveName, wave] of Object.entries(waves.frequencies)) {
            const element = waveElements[waveName];
            if (element) {
                const percentage = (wave.amplitude * 100).toFixed(0);
                element.style.width = `${percentage}%`;
                
                // Marquer l'onde active
                const waveContainer = element.closest('.wave');
                if (wave.active) {
                    waveContainer.classList.add('active');
                } else {
                    waveContainer.classList.remove('active');
                }
            }
        }
        
        if (waves.current_dominant) {
            this.elements.dominantWave.textContent = waves.current_dominant;
        }
    }
    
    /**
     * Met à jour les émotions
     */
    updateEmotions(emotions) {
        const state = emotions.current_emotional_state;
        
        if (state.primary_emotion) {
            this.elements.currentEmotion.textContent = state.primary_emotion;
        }
        
        if (state.intensity !== undefined) {
            this.elements.emotionIntensity.textContent = state.intensity.toFixed(2);
        }
    }
    
    /**
     * Met à jour les statistiques
     */
    updateStats(stats) {
        if (stats.messages_sent !== undefined) {
            this.elements.messagesSent.textContent = stats.messages_sent;
        }
        if (stats.messages_received !== undefined) {
            this.elements.messagesReceived.textContent = stats.messages_received;
        }
        if (stats.brain_updates !== undefined) {
            this.elements.brainUpdates.textContent = stats.brain_updates;
        }
    }

    /**
     * Initialise le système audio
     */
    initializeAudio() {
        // Vérifier le support de l'API Speech
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.audioState.recognition = new SpeechRecognition();
            this.audioState.recognition.continuous = false;
            this.audioState.recognition.interimResults = false;
            this.audioState.recognition.lang = 'fr-FR';

            this.audioState.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.messageInput.value = transcript;
                this.updateAudioStatus('🎤 Transcrit: ' + transcript);
                this.autoResizeTextarea();
            };

            this.audioState.recognition.onerror = (event) => {
                this.updateAudioStatus('❌ Erreur microphone');
                this.audioState.micActive = false;
                this.updateMicButton();
            };

            this.audioState.recognition.onend = () => {
                this.audioState.micActive = false;
                this.updateMicButton();
                this.updateAudioStatus('🎤 Prêt');
            };
        }

        // Initialiser la synthèse vocale
        if ('speechSynthesis' in window) {
            this.audioState.synthesis = window.speechSynthesis;
        }

        this.updateAudioStatus('🎤 Audio prêt');
    }

    /**
     * Initialise le système KYBER
     */
    initializeKyber() {
        this.updateKyberDisplay();
        this.generateNeuronNetwork();

        // Animation des neurones
        setInterval(() => {
            this.animateNeurons();
        }, 100);
    }

    /**
     * Active/désactive le microphone
     */
    toggleMicrophone() {
        if (!this.audioState.recognition) {
            this.updateAudioStatus('❌ Microphone non supporté');
            return;
        }

        if (this.audioState.micActive) {
            this.audioState.recognition.stop();
            this.audioState.micActive = false;
            this.updateAudioStatus('🎤 Arrêté');
        } else {
            this.audioState.recognition.start();
            this.audioState.micActive = true;
            this.updateAudioStatus('🎤 Écoute...');
        }

        this.updateMicButton();
    }

    /**
     * Active/désactive le haut-parleur
     */
    toggleSpeaker() {
        this.audioState.speakerActive = !this.audioState.speakerActive;
        this.updateSpeakerButton();

        if (this.audioState.speakerActive) {
            this.updateAudioStatus('🔊 Haut-parleur activé');
        } else {
            this.updateAudioStatus('🔇 Haut-parleur désactivé');
        }
    }

    /**
     * Active/désactive KYBER
     */
    toggleKyber() {
        this.kyberState.active = !this.kyberState.active;
        this.updateKyberButton();

        if (this.kyberState.active) {
            this.kyberState.currentBoost = 166;
            console.log('🚀 Accélérateurs KYBER ULTRA activés');
        } else {
            this.kyberState.currentBoost = 1;
            console.log('⏸️ Accélérateurs KYBER désactivés');
        }

        this.updateKyberDisplay();
    }

    /**
     * Affiche/masque le système cognitif
     */
    toggleCognitive() {
        this.kyberState.cognitiveVisible = !this.kyberState.cognitiveVisible;
        this.updateCognitiveButton();

        if (this.kyberState.cognitiveVisible) {
            this.elements.cognitiveSystem.style.display = 'block';
            console.log('👁️ Système cognitif affiché');
        } else {
            this.elements.cognitiveSystem.style.display = 'none';
            console.log('👁️ Système cognitif masqué');
        }
    }

    /**
     * Met à jour le bouton microphone
     */
    updateMicButton() {
        if (this.audioState.micActive) {
            this.elements.micButton.classList.add('active');
            this.elements.micButton.innerHTML = '<i class="fas fa-microphone-slash"></i>';
        } else {
            this.elements.micButton.classList.remove('active');
            this.elements.micButton.innerHTML = '<i class="fas fa-microphone"></i>';
        }
    }

    /**
     * Met à jour le bouton haut-parleur
     */
    updateSpeakerButton() {
        if (this.audioState.speakerActive) {
            this.elements.speakerButton.classList.remove('muted');
            this.elements.speakerButton.innerHTML = '<i class="fas fa-volume-up"></i>';
        } else {
            this.elements.speakerButton.classList.add('muted');
            this.elements.speakerButton.innerHTML = '<i class="fas fa-volume-mute"></i>';
        }
    }

    /**
     * Met à jour le bouton KYBER
     */
    updateKyberButton() {
        if (this.kyberState.active) {
            this.elements.kyberToggle.classList.add('active');
        } else {
            this.elements.kyberToggle.classList.remove('active');
        }
    }

    /**
     * Met à jour le bouton cognitif
     */
    updateCognitiveButton() {
        if (this.kyberState.cognitiveVisible) {
            this.elements.cognitiveToggle.classList.add('active');
        } else {
            this.elements.cognitiveToggle.classList.remove('active');
        }
    }

    /**
     * Met à jour l'affichage KYBER
     */
    updateKyberDisplay() {
        this.elements.kyberBoost.textContent = this.kyberState.currentBoost + 'x';
    }

    /**
     * Met à jour le statut audio
     */
    updateAudioStatus(status) {
        this.elements.audioIndicator.textContent = status;
    }

    /**
     * Ajoute une étape cognitive
     */
    addCognitiveStep(step) {
        if (!this.kyberState.cognitiveVisible) return;

        const stepDiv = document.createElement('div');
        stepDiv.className = 'cognitive-step';

        const icon = this.getCognitiveIcon(step.status);
        const delay = step.delay_used || step.delay || 0;

        stepDiv.innerHTML = `
            <div class="step-icon">${icon}</div>
            <div class="step-text">${step.text}</div>
            <div class="step-delay">${delay}ms</div>
        `;

        this.elements.cognitiveSteps.appendChild(stepDiv);

        // Limiter à 10 étapes
        while (this.elements.cognitiveSteps.children.length > 10) {
            this.elements.cognitiveSteps.removeChild(this.elements.cognitiveSteps.firstChild);
        }

        // Scroll vers le bas
        this.elements.cognitiveSteps.scrollTop = this.elements.cognitiveSteps.scrollHeight;
    }

    /**
     * Obtient l'icône pour une étape cognitive
     */
    getCognitiveIcon(status) {
        const icons = {
            thinking: '🧠',
            memory: '💾',
            internet: '🌐',
            processing: '⚡'
        };
        return icons[status] || '🔄';
    }

    /**
     * Génère le réseau de neurones
     */
    generateNeuronNetwork() {
        const network = this.elements.neuronNetwork;
        network.innerHTML = '';

        // Créer 20 neurones aléatoires
        for (let i = 0; i < 20; i++) {
            const neuron = document.createElement('div');
            neuron.className = 'neuron';
            neuron.style.left = Math.random() * 95 + '%';
            neuron.style.top = Math.random() * 95 + '%';
            neuron.style.animationDelay = Math.random() * 3 + 's';
            network.appendChild(neuron);
        }
    }

    /**
     * Anime les neurones
     */
    animateNeurons() {
        const neurons = this.elements.neuronNetwork.querySelectorAll('.neuron');
        neurons.forEach(neuron => {
            if (Math.random() < 0.1) { // 10% de chance d'animation
                neuron.style.boxShadow = '0 0 10px #00ff88';
                setTimeout(() => {
                    neuron.style.boxShadow = '';
                }, 200);
            }
        });
    }

    /**
     * Synthèse vocale d'un message
     */
    speakMessage(text) {
        if (!this.audioState.synthesis || !this.audioState.speakerActive) return;

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'fr-FR';
        utterance.rate = 0.9;
        utterance.pitch = 1.0;

        this.audioState.synthesis.speak(utterance);
    }

    /**
     * Initialise le système de documents
     */
    initializeDocuments() {
        this.updateDocumentStats();
        console.log('📄 Système de documents initialisé');
    }

    /**
     * Active/désactive le mode document
     */
    toggleDocumentMode() {
        this.documentState.documentMode = !this.documentState.documentMode;

        if (this.documentState.documentMode) {
            this.elements.messageInput.classList.add('document-mode');
            this.elements.messageInput.placeholder = 'Collez votre document ici... (PDF, Word, texte, code, etc.)';
            this.elements.messageInput.rows = 8;
            console.log('📄 Mode document activé');
        } else {
            this.elements.messageInput.classList.remove('document-mode');
            this.elements.messageInput.placeholder = 'Tapez votre message, utilisez le microphone, ou collez un document...';
            this.elements.messageInput.rows = 1;
            console.log('📄 Mode document désactivé');
        }

        this.updateDocumentButton();
        this.autoResizeTextarea();
    }

    /**
     * Efface le contenu du document
     */
    clearDocument() {
        this.elements.messageInput.value = '';
        this.documentState.hasDocument = false;
        this.documentState.documentLength = 0;
        this.updateDocumentStats();
        this.autoResizeTextarea();
        console.log('📄 Document effacé');
    }

    /**
     * Détecte automatiquement si un document a été collé
     */
    detectDocument() {
        const content = this.elements.messageInput.value;
        const length = content.length;

        // Considérer comme document si > 500 caractères
        if (length > 500) {
            this.documentState.hasDocument = true;
            this.documentState.documentMode = true;
            this.elements.messageInput.classList.add('document-mode');
            this.elements.messageInput.rows = Math.min(Math.max(8, Math.ceil(length / 100)), 20);
            this.updateDocumentButton();
            console.log('📄 Document détecté automatiquement');
        }

        this.updateDocumentStats();
    }

    /**
     * Met à jour les statistiques du document
     */
    updateDocumentStats() {
        const content = this.elements.messageInput.value;
        const length = content.length;
        const words = content.trim() ? content.trim().split(/\s+/).length : 0;
        const lines = content.split('\n').length;

        this.documentState.documentLength = length;

        if (length > 0) {
            this.elements.documentStats.textContent = `${length} caractères, ${words} mots, ${lines} lignes`;
            this.elements.documentInfo.style.display = 'block';
        } else {
            this.elements.documentInfo.style.display = 'none';
        }

        // Changer la couleur selon la taille
        if (length > 10000) {
            this.elements.documentStats.style.color = '#f44336'; // Rouge pour très long
        } else if (length > 2000) {
            this.elements.documentStats.style.color = '#ff9800'; // Orange pour long
        } else if (length > 500) {
            this.elements.documentStats.style.color = '#4CAF50'; // Vert pour document
        } else {
            this.elements.documentStats.style.color = '#666'; // Gris pour texte normal
        }
    }

    /**
     * Met à jour le bouton document
     */
    updateDocumentButton() {
        if (this.documentState.documentMode) {
            this.elements.documentButton.innerHTML = '<i class="fas fa-file-text"></i><span>Mode Doc</span>';
            this.elements.documentButton.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
        } else {
            this.elements.documentButton.innerHTML = '<i class="fas fa-file-text"></i><span>Document</span>';
            this.elements.documentButton.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
        }
    }
}

// Initialiser l'interface quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.chatInterface = new ChatInterface();
    window.memoryMonitor = new MemoryMonitor();
    console.log('🚀 Interface chat prête !');
});

/**
 * 🧠 MONITEUR DE MÉMOIRE THERMIQUE EN TEMPS RÉEL
 */
class MemoryMonitor {
    constructor() {
        this.socket = io('http://localhost:3000');
        this.collapsed = false;
        this.lastUpdate = null;
        this.activityHistory = [];

        this.elements = {
            monitor: document.getElementById('memoryMonitor'),
            content: document.getElementById('memoryContent'),
            toggle: document.getElementById('memoryToggle'),
            status: document.getElementById('memoryStatus'),
            totalEntries: document.getElementById('totalEntries'),
            qiLevel: document.getElementById('qiLevel'),
            zones: document.getElementById('memoryZones'),
            activity: document.getElementById('memoryActivity')
        };

        this.initializeMemoryMonitor();
        this.setupMemoryListeners();

        console.log('🧠 Moniteur de mémoire thermique initialisé');
    }

    /**
     * Initialise le moniteur de mémoire
     */
    initializeMemoryMonitor() {
        // Demander l'état initial de la mémoire
        this.socket.emit('get_memory_state');

        // Démarrer la surveillance
        this.startMemoryPolling();

        // Créer les zones de mémoire
        this.createMemoryZones();
    }

    /**
     * Configure les listeners pour la mémoire
     */
    setupMemoryListeners() {
        // État de la mémoire
        this.socket.on('memory_state', (memoryData) => {
            this.updateMemoryDisplay(memoryData);
        });

        // Changements de mémoire
        this.socket.on('memory_changed', (changes) => {
            this.handleMemoryChanges(changes);
        });

        // Nouvelle entrée en mémoire
        this.socket.on('memory_entry_added', (entry) => {
            this.handleNewMemoryEntry(entry);
        });

        // Erreur de mémoire
        this.socket.on('memory_error', (error) => {
            console.error('❌ Erreur mémoire:', error);
            this.elements.status.style.color = '#ff6b6b';
            this.elements.status.textContent = '●';
        });
    }

    /**
     * Démarre la surveillance périodique
     */
    startMemoryPolling() {
        setInterval(() => {
            this.socket.emit('get_memory_state');
        }, 2000); // Toutes les 2 secondes
    }

    /**
     * Crée les zones de mémoire
     */
    createMemoryZones() {
        const zoneNames = [
            { name: 'procedural', label: 'PROCÉDURALE', color: '#00ff88' },
            { name: 'episodic', label: 'ÉPISODIQUE', color: '#4fc3f7' },
            { name: 'semantic', label: 'SÉMANTIQUE', color: '#ff9800' },
            { name: 'working', label: 'TRAVAIL', color: '#e91e63' },
            { name: 'emotional', label: 'ÉMOTIONNELLE', color: '#9c27b0' },
            { name: 'meta', label: 'MÉTA', color: '#ffd93d' }
        ];

        this.elements.zones.innerHTML = '';

        for (const zone of zoneNames) {
            const zoneDiv = document.createElement('div');
            zoneDiv.className = 'zone-item';
            zoneDiv.id = `zone-${zone.name}`;
            zoneDiv.style.borderLeftColor = zone.color;

            zoneDiv.innerHTML = `
                <div class="zone-header">
                    <div class="zone-name" style="color: ${zone.color}">${zone.label}</div>
                    <div class="zone-temp">37.0°C</div>
                </div>
                <div class="zone-progress">
                    <div class="zone-progress-bar" style="background: linear-gradient(90deg, ${zone.color}, ${zone.color}80)"></div>
                </div>
                <div class="zone-info">
                    <span class="zone-entries">0 entrées</span>
                    <span class="zone-usage">0%</span>
                </div>
            `;

            this.elements.zones.appendChild(zoneDiv);
        }
    }

    /**
     * Met à jour l'affichage de la mémoire
     */
    updateMemoryDisplay(memoryData) {
        if (!memoryData) return;

        // Mettre à jour les statistiques globales
        this.elements.totalEntries.textContent = memoryData.total_entries || 0;
        this.elements.qiLevel.textContent = memoryData.qi_level || 0;

        // Statut de connexion
        this.elements.status.style.color = '#00ff88';
        this.elements.status.textContent = '●';

        // Mettre à jour chaque zone
        if (memoryData.zones) {
            for (const [zoneName, zoneData] of Object.entries(memoryData.zones)) {
                this.updateZoneDisplay(zoneName, zoneData);
            }
        }

        this.lastUpdate = Date.now();
    }

    /**
     * Met à jour l'affichage d'une zone
     */
    updateZoneDisplay(zoneName, zoneData) {
        const zoneElement = document.getElementById(`zone-${zoneName}`);
        if (!zoneElement) return;

        const tempElement = zoneElement.querySelector('.zone-temp');
        const progressBar = zoneElement.querySelector('.zone-progress-bar');
        const entriesElement = zoneElement.querySelector('.zone-entries');
        const usageElement = zoneElement.querySelector('.zone-usage');

        // Température
        if (tempElement && zoneData.temperature) {
            tempElement.textContent = `${zoneData.temperature.toFixed(1)}°C`;
        }

        // Barre de progression
        if (progressBar && zoneData.usage_percent !== undefined) {
            progressBar.style.width = `${zoneData.usage_percent}%`;
        }

        // Nombre d'entrées
        if (entriesElement && zoneData.entries_count !== undefined) {
            entriesElement.textContent = `${zoneData.entries_count} entrées`;
        }

        // Pourcentage d'utilisation
        if (usageElement && zoneData.usage_percent !== undefined) {
            usageElement.textContent = `${zoneData.usage_percent}%`;
        }
    }

    /**
     * Gère les changements de mémoire
     */
    handleMemoryChanges(changes) {
        for (const change of changes) {
            this.addActivityItem(change);

            // Animation de la zone modifiée
            if (change.zone) {
                const zoneElement = document.getElementById(`zone-${change.zone}`);
                if (zoneElement) {
                    zoneElement.classList.add('updated');
                    setTimeout(() => {
                        zoneElement.classList.remove('updated');
                    }, 500);
                }
            }
        }
    }

    /**
     * Gère une nouvelle entrée en mémoire
     */
    handleNewMemoryEntry(entry) {
        this.addActivityItem({
            type: 'new_entry',
            zone: entry.zone,
            entry: entry,
            timestamp: Date.now()
        });
    }

    /**
     * Ajoute un élément d'activité
     */
    addActivityItem(activity) {
        const activityDiv = document.createElement('div');
        activityDiv.className = 'activity-item new';

        let activityText = '';
        switch (activity.type) {
            case 'new_entry':
                activityText = `Nouvelle entrée en zone ${activity.zone.toUpperCase()}`;
                break;
            case 'zone_entries_change':
                const diff = activity.difference > 0 ? `+${activity.difference}` : activity.difference;
                activityText = `Zone ${activity.zone.toUpperCase()}: ${diff} entrées`;
                break;
            case 'qi_change':
                const qiDiff = activity.new_value - activity.old_value;
                const qiSign = qiDiff > 0 ? '+' : '';
                activityText = `QI: ${qiSign}${qiDiff} (${activity.new_value})`;
                break;
            case 'temperature_change':
                activityText = `Température ${activity.zone.toUpperCase()}: ${activity.new_temp.toFixed(1)}°C`;
                break;
            default:
                activityText = `${activity.type}: ${JSON.stringify(activity)}`;
        }

        activityDiv.innerHTML = `
            <div class="activity-type">${activity.type.replace('_', ' ')}</div>
            <div class="activity-details">${activityText}</div>
        `;

        // Ajouter en haut de la liste
        this.elements.activity.insertBefore(activityDiv, this.elements.activity.firstChild);

        // Limiter à 20 éléments
        const items = this.elements.activity.querySelectorAll('.activity-item');
        if (items.length > 20) {
            items[items.length - 1].remove();
        }

        // Retirer l'animation après un délai
        setTimeout(() => {
            activityDiv.classList.remove('new');
        }, 300);

        // Garder l'historique
        this.activityHistory.unshift(activity);
        if (this.activityHistory.length > 100) {
            this.activityHistory = this.activityHistory.slice(0, 100);
        }
    }
}

/**
 * Bascule l'état du moniteur de mémoire
 */
function toggleMemoryMonitor() {
    const monitor = document.getElementById('memoryMonitor');
    const content = document.getElementById('memoryContent');
    const toggle = document.getElementById('memoryToggle');

    if (monitor.classList.contains('collapsed')) {
        monitor.classList.remove('collapsed');
        content.style.display = 'block';
        toggle.textContent = '−';
    } else {
        monitor.classList.add('collapsed');
        content.style.display = 'none';
        toggle.textContent = '+';
    }
}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Interface avec Statut Temps Réel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-weight: bold;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-idle { background: #4CAF50; }
        .status-thinking { background: #FF9800; }
        .status-memory { background: #2196F3; }
        .status-internet { background: #9C27B0; }
        .status-error { background: #F44336; }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .spinner {
            animation: spin 1s linear infinite;
        }

        .main-container {
            flex: 1;
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            gap: 20px;
            padding: 20px;
        }

        .chat-container {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .status-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        .status-panel h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .activity-log {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .log-entry.thinking { background: #fff3cd; }
        .log-entry.memory { background: #d1ecf1; }
        .log-entry.internet { background: #e2e3f1; }
        .log-entry.success { background: #d4edda; }
        .log-entry.error { background: #f8d7da; }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .input-container {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .neural-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .stat-value {
            font-weight: bold;
            color: #667eea;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 LOUNA AI - Assistant Intelligent</h1>
        <p style="color: rgba(255,255,255,0.8);">Interface avec Monitoring Temps Réel</p>
    </div>

    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-icon status-idle" id="statusIcon"></div>
            <span id="statusText">🟢 Prêt - En attente</span>
        </div>
        <div class="status-indicator">
            <span id="responseTime">⚡ Temps de réponse: --</span>
        </div>
        <div class="status-indicator">
            <span id="connectionStatus">🔗 Connecté</span>
        </div>
    </div>

    <div class="main-container">
        <div class="chat-container">
            <div class="messages" id="messages"></div>
            <div class="input-container">
                <div class="input-group">
                    <input type="text" class="message-input" id="messageInput" 
                           placeholder="Posez votre question à Louna..." 
                           onkeypress="handleKeyPress(event)">
                    <button class="send-button" id="sendButton" onclick="sendMessage()">
                        <span id="buttonText">Envoyer</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="status-panel">
            <h3>📊 Statut Temps Réel</h3>
            
            <div class="neural-stats">
                <h4>🧠 Statistiques Neurales</h4>
                <div class="stat-item">
                    <span>QI Level:</span>
                    <span class="stat-value" id="qiLevel">185</span>
                </div>
                <div class="stat-item">
                    <span>Neurones Actifs:</span>
                    <span class="stat-value" id="activeNeurons">86M</span>
                </div>
                <div class="stat-item">
                    <span>Température:</span>
                    <span class="stat-value" id="temperature">37.2°C</span>
                </div>
                <div class="stat-item">
                    <span>Mémoires:</span>
                    <span class="stat-value" id="totalMemories">97</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="performanceBar" style="width: 85%"></div>
                </div>
            </div>

            <div class="activity-log">
                <h4>📝 Journal d'Activité</h4>
                <div id="activityLog">
                    <div class="log-entry success">
                        <span>✅</span>
                        <span>Système initialisé</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isProcessing = false;
        let startTime = 0;

        // Fonction pour mettre à jour le statut
        function updateStatus(status, text, icon = null) {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            
            statusIcon.className = `status-icon ${status}`;
            if (icon) {
                statusIcon.innerHTML = icon;
                statusIcon.classList.add('spinner');
            } else {
                statusIcon.innerHTML = '';
                statusIcon.classList.remove('spinner');
            }
            
            statusText.textContent = text;
            
            // Ajouter au journal d'activité
            addToActivityLog(status, text);
        }

        // Fonction pour ajouter une entrée au journal
        function addToActivityLog(type, message) {
            const log = document.getElementById('activityLog');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const icon = getStatusIcon(type);
            const time = new Date().toLocaleTimeString();
            
            entry.innerHTML = `
                <span>${icon}</span>
                <span>[${time}] ${message}</span>
            `;
            
            log.insertBefore(entry, log.firstChild);
            
            // Garder seulement les 20 dernières entrées
            while (log.children.length > 20) {
                log.removeChild(log.lastChild);
            }
        }

        function getStatusIcon(type) {
            const icons = {
                'idle': '🟢',
                'thinking': '🧠',
                'memory': '💾',
                'internet': '🌐',
                'success': '✅',
                'error': '❌'
            };
            return icons[type] || '📝';
        }

        // Fonction pour envoyer un message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isProcessing) return;
            
            isProcessing = true;
            startTime = Date.now();
            
            // Mettre à jour l'interface
            updateStatus('thinking', '🧠 Réflexion en cours...', '⚙️');
            document.getElementById('sendButton').disabled = true;
            document.getElementById('buttonText').textContent = 'Traitement...';
            
            // Ajouter le message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            try {
                // Simuler les étapes de traitement
                await simulateProcessingSteps();
                
                // Envoyer la requête
                const response = await fetch('http://localhost:5002/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('success', '✅ Réponse générée');
                    addMessage(data.response, 'bot');
                    
                    // Mettre à jour les statistiques
                    updateNeuralStats(data);
                } else {
                    throw new Error(data.error || 'Erreur inconnue');
                }
                
            } catch (error) {
                updateStatus('error', `❌ Erreur: ${error.message}`);
                addMessage(`Désolé, une erreur s'est produite: ${error.message}`, 'bot');
            } finally {
                // Calculer le temps de réponse
                const responseTime = Date.now() - startTime;
                document.getElementById('responseTime').textContent = `⚡ Temps: ${responseTime}ms`;
                
                // Réinitialiser l'interface
                setTimeout(() => {
                    updateStatus('idle', '🟢 Prêt - En attente');
                    document.getElementById('sendButton').disabled = false;
                    document.getElementById('buttonText').textContent = 'Envoyer';
                    isProcessing = false;
                }, 1000);
            }
        }

        // Simuler les étapes de traitement
        async function simulateProcessingSteps() {
            const steps = [
                { status: 'thinking', text: '🧠 Analyse de la question...', delay: 300 },
                { status: 'memory', text: '💾 Consultation mémoire thermique...', delay: 500 },
                { status: 'internet', text: '🌐 Recherche d\'informations...', delay: 700 },
                { status: 'thinking', text: '🧠 Génération de la réponse...', delay: 400 }
            ];
            
            for (const step of steps) {
                updateStatus(step.status, step.text);
                await new Promise(resolve => setTimeout(resolve, step.delay));
            }
        }

        // Mettre à jour les statistiques neurales
        function updateNeuralStats(data) {
            if (data.neural_stats) {
                document.getElementById('qiLevel').textContent = data.neural_stats.qi_level || '185';
                document.getElementById('temperature').textContent = 
                    (data.neural_stats.temperature || 37.2).toFixed(1) + '°C';
                document.getElementById('totalMemories').textContent = 
                    data.neural_stats.total_memories || '97';
                
                // Mettre à jour la barre de performance
                const performance = Math.min(100, (data.neural_stats.qi_level || 185) / 2);
                document.getElementById('performanceBar').style.width = performance + '%';
            }
        }

        // Ajouter un message à la conversation
        function addMessage(text, sender) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isProcessing) {
                sendMessage();
            }
        }

        // Vérifier la connexion périodiquement
        setInterval(async () => {
            try {
                const response = await fetch('http://localhost:5002/health');
                if (response.ok) {
                    document.getElementById('connectionStatus').textContent = '🔗 Connecté';
                } else {
                    document.getElementById('connectionStatus').textContent = '⚠️ Connexion instable';
                }
            } catch (error) {
                document.getElementById('connectionStatus').textContent = '❌ Déconnecté';
            }
        }, 5000);

        // Message de bienvenue
        window.onload = function() {
            addMessage("Bonjour ! Je suis Louna, votre assistant IA. Vous pouvez maintenant voir en temps réel ce que je fais grâce au panneau de statut. Posez-moi une question !", 'bot');
        };
    </script>
</body>
</html>
